"use client"

import { useEffect } from "react"
import { useTranslations } from "next-intl"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { zodResolver } from "@hookform/resolvers/zod"
import {
	Button,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	Input,
	Textarea,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@repo/ui/components"
import { useForm } from "react-hook-form"
import { z } from "zod"
import ImageUploader from "@/lib/components/ImageUploader"
import MdxEditorField from "@/lib/components/mdx-editor/MdxEditorField"
import { ArticlePost, GameCategory } from "@repo/shared-types"

interface ArticleEditorProps {
	article: ArticlePost
	onSave: (article: ArticlePost) => void
	projectId: string
	isLoading?: boolean
	categories?: GameCategory[]
}

// 定义表单验证模式
const formSchema = z.object({
	title: z.string().min(1, { message: "标题不能为空" }).max(70),
	slug: z.string().min(1, { message: "访问路径不能为空" }).optional(),
	content: z.string(),
	titleImageUrl: z.string().optional(),
	author: z.string().optional(),
	authorImageUrl: z.string().optional(),
	readTime: z.string().optional(),
	tags: z.array(z.string()).optional(),
	categoryCode: z.string().optional(),
	metadata: z.object({
		title: z.string().max(70).optional(),
		description: z.string().max(200).optional(),
		ogTitle: z.string().max(70).optional(),
		ogDescription: z.string().max(200).optional(),
		ogImage: z.string().optional(),
		ogUrl: z.string().optional(),
		twitterCard: z.string().optional(),
		twitterTitle: z.string().optional(),
		twitterDescription: z.string().optional(),
		twitterImage: z.string().optional(),
		structuredData: z.record(z.any()).optional(),
	}),
})

export default function ArticleEditor({
	article,
	onSave,
	projectId,
	isLoading = false,
	categories = [],
}: ArticleEditorProps) {
	// 获取语言上下文
	const t = useTranslations("Articles")
	const { currentLanguage } = useTranslationContext()

	// 判断是否为新建模式（通过检查文章ID是否以"article-"开头）
	const isNewArticle = article.id.startsWith("article-")

	// 定义表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: article.title || "",
			slug: article.slug || "",
			content: article.mdxContent || "",
			titleImageUrl: article.titleImageUrl || "",
			author: article.author || "",
			authorImageUrl: article.authorImageUrl || "",
			readTime: article.readTime || "",
			tags: article.tags || [],
			categoryCode: article.category?.id || "",
			metadata: {
				title: article.metadata?.title || "",
				description: article.metadata?.description || "",
				ogTitle: article.metadata?.ogTitle || "",
				ogDescription: article.metadata?.ogDescription || "",
				ogImage: article.metadata?.ogImage || "",
				ogUrl: article.metadata?.ogUrl || "",
				twitterCard: article.metadata?.twitterCard || "",
				twitterTitle: article.metadata?.twitterTitle || "",
				twitterDescription: article.metadata?.twitterDescription || "",
				twitterImage: article.metadata?.twitterImage || "",
				structuredData: article.metadata?.structuredData || {},
			},
		},
	})

	// 当文章数据变化时更新表单
	useEffect(() => {
		if (article) {
			form.reset({
				title: article.title || "",
				slug: article.slug || "",
				content: article.mdxContent || "",
				titleImageUrl: article.titleImageUrl || "",
				author: article.author || "",
				authorImageUrl: article.authorImageUrl || "",
				readTime: article.readTime || "",
				tags: article.tags || [],
				categoryCode: article.category?.id || "",
				metadata: {
					title: article.metadata?.title || "",
					description: article.metadata?.description || "",
					ogTitle: article.metadata?.ogTitle || "",
					ogDescription: article.metadata?.ogDescription || "",
					ogImage: article.metadata?.ogImage || "",
					ogUrl: article.metadata?.ogUrl || "",
					twitterCard: article.metadata?.twitterCard || "",
					twitterTitle: article.metadata?.twitterTitle || "",
					twitterDescription: article.metadata?.twitterDescription || "",
					twitterImage: article.metadata?.twitterImage || "",
					structuredData: article.metadata?.structuredData || {},
				},
			})
		}
	}, [article, form])

	// 处理表单提交
	function onSubmit(values: z.infer<typeof formSchema>) {
		// 根据 categoryCode 找到对应的分类信息
		const selectedCategory = values.categoryCode
			? categories.find((cat) => cat.code === values.categoryCode)
			: undefined

		// 构建更新后的文章对象
		const updatedArticle: ArticlePost & { categoryCode?: string } = {
			...article,
			title: values.title,
			slug: values.slug || article.slug,
			mdxContent: values.content,
			titleImageUrl: values.titleImageUrl || "",
			author: values.author || undefined,
			authorImageUrl: values.authorImageUrl || undefined,
			readTime: values.readTime || undefined,
			tags: values.tags,
			categoryCode: values.categoryCode || undefined,
			category: selectedCategory
				? {
						id: selectedCategory.code,
						name: selectedCategory.name,
						slug: selectedCategory.slug || selectedCategory.code,
						count: selectedCategory.count || 0,
					}
				: undefined,
			locale: currentLanguage,
			metadata: {
				title: values.metadata.title || "",
				description: values.metadata.description || "",
				ogTitle: values.metadata.ogTitle || "",
				ogDescription: values.metadata.ogDescription || "",
				ogImage: values.metadata.ogImage || "",
				ogUrl: values.metadata.ogUrl || "",
				twitterCard: values.metadata.twitterCard || "",
				twitterTitle: values.metadata.twitterTitle || "",
				twitterDescription: values.metadata.twitterDescription || "",
				twitterImage: values.metadata.twitterImage || "",
				structuredData: values.metadata.structuredData || {},
			},
		}

		onSave(updatedArticle as ArticlePost)
	}

	function handleReset() {
		form.reset({
			title: article.title || "",
			slug: article.slug || "",
			content: article.mdxContent || "",
			titleImageUrl: article.titleImageUrl || "",
			author: article.author || "",
			authorImageUrl: article.authorImageUrl || "",
			readTime: article.readTime || "",
			tags: article.tags || [],
			categoryCode: article.category?.id || "",
			metadata: {
				title: article.metadata?.title || "",
				description: article.metadata?.description || "",
				ogTitle: article.metadata?.ogTitle || "",
				ogDescription: article.metadata?.ogDescription || "",
				ogImage: article.metadata?.ogImage || "",
				ogUrl: article.metadata?.ogUrl || "",
				twitterCard: article.metadata?.twitterCard || "",
				twitterTitle: article.metadata?.twitterTitle || "",
				twitterDescription: article.metadata?.twitterDescription || "",
				twitterImage: article.metadata?.twitterImage || "",
				structuredData: article.metadata?.structuredData || {},
			},
		})
	}

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				onReset={handleReset}
				className="space-y-6"
			>
				{/* 重新设计的表单布局 */}
				<div className="space-y-8">
					{/* 第一组：核心内容信息 */}
					<div className="space-y-4">
						<div className="flex items-center gap-2 pb-3 border-b border-border">
							<div className="w-1 h-4 bg-primary rounded-full"></div>
							<h3 className="text-sm font-semibold text-foreground">
								基本信息
							</h3>
						</div>

						<div className="grid md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="title"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium flex items-center gap-1">
											文章标题
											<span className="text-destructive">*</span>
										</FormLabel>
										<FormControl>
											<Input
												placeholder="请输入文章标题"
												{...field}
												maxLength={70}
												className="h-10"
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground">
											{field.value?.length || 0}/70 字符
										</FormDescription>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="categoryCode"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											文章分类
										</FormLabel>
										<Select onValueChange={field.onChange} value={field.value || ""}>
											<FormControl>
												<SelectTrigger className="h-10">
													<SelectValue placeholder="选择分类" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{categories.map((category) => (
													<SelectItem key={category.code} value={category.code}>
														{category.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</FormItem>
								)}
							/>
						</div>

						{/* 条件显示 slug 字段 - 仅在编辑现有文章时显示 */}
						{!isNewArticle && (
							<div className="grid md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="slug"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												访问路径
											</FormLabel>
											<FormControl>
												<Input
													placeholder="请输入文章访问路径"
													{...field}
													className="h-10"
												/>
											</FormControl>
											<FormDescription className="text-xs text-muted-foreground">
												文章的URL路径，用于生成文章链接
											</FormDescription>
										</FormItem>
									)}
								/>
								<div></div>
							</div>
						)}

						<div className="grid md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="titleImageUrl"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											封面图片
										</FormLabel>
										<FormControl>
											<ImageUploader
												variant="cover"
												size="md"
												value={field.value}
												onChange={field.onChange}
												projectId={projectId}
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground">
											建议尺寸：1200x630px，用于文章展示
										</FormDescription>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="readTime"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											预计阅读时间
										</FormLabel>
										<FormControl>
											<Input
												placeholder="例如：5分钟"
												{...field}
												className="h-10"
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground">
											帮助读者了解文章长度
										</FormDescription>
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* 第二组：作者信息 */}
					<div className="space-y-4">
						<div className="flex items-center gap-2 pb-3 border-b border-border">
							<div className="w-1 h-4 bg-blue-500 rounded-full"></div>
							<h3 className="text-sm font-semibold text-foreground">
								作者信息
							</h3>
						</div>

						<div className="grid md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="author"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											作者姓名
										</FormLabel>
										<FormControl>
											<Input
												placeholder="请输入作者姓名"
												{...field}
												className="h-10"
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="authorImageUrl"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											作者头像
										</FormLabel>
										<FormControl>
											<ImageUploader
												variant="avatar"
												size="md"
												value={field.value}
												onChange={field.onChange}
												projectId={projectId}
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground"></FormDescription>
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* 第三组：SEO设置 */}
					<div className="space-y-4">
						<div className="flex items-center gap-2 pb-3 border-b border-border">
							<div className="w-1 h-4 bg-green-500 rounded-full"></div>
							<h3 className="text-sm font-semibold text-foreground">SEO设置</h3>
							<span className="text-xs text-muted-foreground ml-auto">
								搜索引擎优化
							</span>
						</div>

						<div className="grid md:grid-cols-2 gap-6">
							<FormField
								control={form.control}
								name="metadata.title"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											SEO标题
										</FormLabel>
										<FormControl>
											<Input
												placeholder="请输入SEO标题（可选）"
												{...field}
												maxLength={70}
												className="h-10"
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground">
											{field.value?.length || 0}/70 字符，显示在搜索结果中
										</FormDescription>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="metadata.description"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											SEO描述
										</FormLabel>
										<FormControl>
											<Textarea
												placeholder="请输入SEO描述，简要概括文章内容（可选）"
												{...field}
												maxLength={200}
												rows={3}
												className="resize-none"
											/>
										</FormControl>
										<FormDescription className="text-xs text-muted-foreground">
											{field.value?.length || 0}/200 字符，搜索引擎摘要显示
										</FormDescription>
									</FormItem>
								)}
							/>
						</div>
					</div>
				</div>

				{/* 内容编辑器 - 保持在tabs外部 */}
				<div className="mt-6">
					<MdxEditorField name="content" label={t("content")} />
				</div>

				{/* 底部固定按钮 */}
				<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
					<div className="max-w-6xl mx-auto flex justify-end w-full">
						<Button
							type="reset"
							variant="outline"
							className="mr-3"
							loading={isLoading}
						>
							{t("reset")}
						</Button>
						<Button type="submit" loading={isLoading}>
							{t("saveSettings")}
						</Button>
					</div>
				</div>
			</form>
		</Form>
	)
}
