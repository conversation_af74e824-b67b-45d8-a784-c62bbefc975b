"use client"

import {
	Button,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
	Input,
	Label,
	Textarea,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@repo/ui/components"
import { Plus, Trash2, MessageCircle, Star } from "lucide-react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useEffect } from "react"
import { cn } from "@repo/ui/utils"

// 用户评论项目类型定义
export interface UserCommentItem {
	name: string
	rating: number
	content: string
	publishDate: string
}

// 表单验证模式
const userCommentsFormSchema = z.object({
	comments: z.array(
		z.object({
			name: z.string().min(1, "评论者姓名不能为空"),
			rating: z.number().min(1, "评分不能少于1分").max(5, "评分不能超过5分"),
			content: z.string().min(1, "评论内容不能为空"),
			publishDate: z.string().min(1, "发布日期不能为空"),
		})
	),
})

type UserCommentsFormData = z.infer<typeof userCommentsFormSchema>

interface UserCommentsEditorProps {
	comments: UserCommentItem[]
	onChange: (comments: UserCommentItem[]) => void
}

export default function UserCommentsEditor({ comments, onChange }: UserCommentsEditorProps) {
	// 设置表单
	const form = useForm<UserCommentsFormData>({
		resolver: zodResolver(userCommentsFormSchema),
		defaultValues: {
			comments: comments.length > 0 ? comments : [{ 
				name: "", 
				rating: 5, 
				content: "", 
				publishDate: new Date().toISOString().split('T')[0] 
			}],
		},
	})

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "comments",
	})

	// 当外部comments数据变化时更新表单
	useEffect(() => {
		const newComments = comments.length > 0 ? comments : [{ 
			name: "", 
			rating: 5, 
			content: "", 
			publishDate: new Date().toISOString().split('T')[0] 
		}]
		form.reset({ comments: newComments })
	}, [comments, form])

	// 监听表单变化并通知父组件
	const watchedComments = form.watch("comments")
	useEffect(() => {
		// 过滤掉空的评论项目
		const validComments = watchedComments.filter(comment => 
			comment.name.trim() || comment.content.trim()
		)
		onChange(validComments)
	}, [watchedComments, onChange])

	// 添加新的评论项目
	const addComment = () => {
		append({ 
			name: "", 
			rating: 5, 
			content: "", 
			publishDate: new Date().toISOString().split('T')[0] 
		})
	}

	// 删除评论项目
	const removeComment = (index: number) => {
		if (fields.length > 1) {
			remove(index)
		}
	}

	// 渲染星级评分
	const renderStarRating = (rating: number) => {
		return (
			<div className="flex items-center gap-1">
				{[1, 2, 3, 4, 5].map((star) => (
					<Star
						key={star}
						className={cn(
							"h-4 w-4",
							star <= rating 
								? "fill-yellow-400 text-yellow-400" 
								: "text-muted-foreground"
						)}
					/>
				))}
			</div>
		)
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<Label className="text-base font-medium">用户评论</Label>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={addComment}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					添加评论
				</Button>
			</div>

			<div className="space-y-4">
				{fields.map((field, index) => (
					<div
						key={field.id}
						className="border rounded-lg p-4 space-y-4 bg-card"
					>
						{/* 评论标题行 */}
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<MessageCircle className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">评论 {index + 1}</span>
							</div>
							{fields.length > 1 && (
								<Button
									type="button"
									variant="ghost"
									size="icon"
									onClick={() => removeComment(index)}
									className="h-8 w-8 text-muted-foreground hover:text-destructive"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							)}
						</div>

						<div className="grid md:grid-cols-2 gap-4">
							{/* 评论者姓名 */}
							<FormField
								control={form.control}
								name={`comments.${index}.name`}
								render={({ field }) => (
									<FormItem>
										<Label htmlFor={`name-${index}`} className="text-sm font-medium">
											评论者姓名
										</Label>
										<FormControl>
											<Input
												{...field}
												id={`name-${index}`}
												placeholder="请输入评论者姓名..."
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* 发布日期 */}
							<FormField
								control={form.control}
								name={`comments.${index}.publishDate`}
								render={({ field }) => (
									<FormItem>
										<Label htmlFor={`publishDate-${index}`} className="text-sm font-medium">
											发布日期
										</Label>
										<FormControl>
											<Input
												{...field}
												id={`publishDate-${index}`}
												type="date"
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* 评分选择 */}
						<FormField
							control={form.control}
							name={`comments.${index}.rating`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`rating-${index}`} className="text-sm font-medium">
										评分 (1-5分)
									</Label>
									<div className="flex items-center gap-4">
										<Select
											value={field.value.toString()}
											onValueChange={(value) => field.onChange(parseInt(value))}
										>
											<SelectTrigger className="w-32">
												<SelectValue placeholder="选择评分" />
											</SelectTrigger>
											<SelectContent>
												{[1, 2, 3, 4, 5].map((rating) => (
													<SelectItem key={rating} value={rating.toString()}>
														{rating} 分
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										{renderStarRating(field.value)}
									</div>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* 评论内容 */}
						<FormField
							control={form.control}
							name={`comments.${index}.content`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`content-${index}`} className="text-sm font-medium">
										评论内容
									</Label>
									<FormControl>
										<Textarea
											{...field}
											id={`content-${index}`}
											placeholder="请输入评论内容..."
											className="w-full min-h-[100px] resize-none"
											rows={4}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				))}
			</div>

			{fields.length === 0 && (
				<div className="text-center py-8 text-muted-foreground">
					<MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
					<p>暂无用户评论</p>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={addComment}
						className="mt-2"
					>
						添加第一条评论
					</Button>
				</div>
			)}

			<p className="text-xs text-muted-foreground">
				用户评论将展示在游戏详情页面中，帮助其他用户了解游戏的真实评价和体验。
			</p>
		</div>
	)
}
