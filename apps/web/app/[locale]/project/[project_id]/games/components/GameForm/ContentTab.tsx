"use client"

import { IconSelector } from "@/lib/components/icons/IconSelector"
import TranslationSwitcher from "@/lib/components/translation/TranslationSwitcher"
import { MdxEditor } from "@lib/components"
import { Icon } from "@repo/web/lib/components/icons/Icon"
import { GameDetailContentType, Language, ProjectLanguage } from "@repo/shared-types"
import {
	Button,
	Card,
	CardContent,
	CardDescription, CardHeader,
	CardTitle, Form,
	Input,
	Label,
	Checkbox,
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@repo/ui/components"
import { useTranslations } from "next-intl"

import { arrayMove } from "@dnd-kit/sortable"
import { zodResolver } from "@hookform/resolvers/zod"
import { fetchPost } from "@repo/utils/react"
import { Plus } from "lucide-react"
import { useCallback, useEffect, useMemo, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { cn } from "@repo/ui/utils"
import {
	ContentModule, useGameContentsForm
} from "../../hooks/useGameContentsForm"
import ContentModuleList from "./ContentModuleList"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { availableModules } from "./ContentConst"
import FAQEditor from "./FAQEditor"
import UserCommentsEditor from "./UserCommentsEditor"

interface ContentTabProps {
	projectId: string
	gameId: string
	defaultLanguage?: ProjectLanguage
	languages?: Language[]
}

// 定义表单验证模式
const formSchema = z.object({
	modules: z.array(
		z.object({
			id: z.string().optional(),
			tabId: z.string(),
			type: z.enum([
				GameDetailContentType.Article as GameDetailContentType,
				GameDetailContentType.Comments as GameDetailContentType
			]),
			title: z.string().min(1, "模块标题不能为空"),
			text: z.string(),
			jsonContent: z.any().optional(),
			icon: z.string().optional(),
			operation: z.enum(["create", "update", "delete"]).optional(),
		}),
	),
})

export default function ContentTab({
	projectId,
	gameId,
	defaultLanguage = ProjectLanguage.EN,
	languages = [],
}: ContentTabProps) {
	const {
		data: contentData = { modules: [] },
		localeData,
		locale,
		updateContentData,
		handleSave,
		isLoading,
		trigger,
	} = useGameContentsForm(projectId, gameId)

	const t = useTranslations("Games")
	const { currentLanguage } = useTranslationContext()
	const [selectedModuleId, setSelectedModuleId] = useState<string | null>(null)
	const selectOriginalModule = useMemo<ContentModule | null>(
		() => contentData.modules.find((m) => m.tabId === selectedModuleId) ?? null,
		[contentData.modules, selectedModuleId],
	)
	// 获取当前选中的模块
	const selectedModule = useMemo<ContentModule | null>(() => {
		if (locale !== currentLanguage) {
			return (
				localeData ??
				({
					id: "",
					title: "",
					tabId: "",
					type: GameDetailContentType.Article,
					text: "",
					jsonContent: {},
					icon: "",
					operation: "update",
				} as unknown as ContentModule)
			)
		}
		return selectOriginalModule
	}, [selectOriginalModule, localeData, locale])

	// 设置表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			modules: contentData.modules.map(module => ({
							id: module.id,
							tabId: module.tabId,
							title: module.title || "",
							text: module.text || "",
							icon: module.icon,
							jsonContent: module.jsonContent || {},
							operation: module.operation,
							type: module.type || GameDetailContentType.Article // 使用模块的实际类型
						}))
					} as const,
			})

		// 当contentData变化时更新表单值
	useEffect(() => {
		if (contentData) {
			// 准备新的表单数据，保持模块的实际类型
			const newFormData = {
				modules: contentData.modules.map(module => ({
					id: module.id, // 确保包含id字段
					tabId: module.tabId,
					title: module.title || "",
					text: module.text || "",
					icon: module.icon,
					jsonContent: module.jsonContent || {},
					operation: module.operation,
					type: module.type || GameDetailContentType.Article // 使用模块的实际类型
				}))
			} as const;

			// 使用JSON.stringify进行深度比较，避免循环更新
			const currentFormValues = form.getValues();
			if (JSON.stringify(currentFormValues) !== JSON.stringify(newFormData)) {
				form.reset(newFormData);
			}
		}
	}, [contentData])



	// 当没有模块时，自动选择第一个模块
	useEffect(() => {
		if (contentData.modules.length > 0 && !selectedModuleId) {
			setSelectedModuleId(contentData.modules[0]?.tabId || null)
		}
	}, [contentData.modules, selectedModuleId])

	// 当选择一个模块时
	const handleSelectModule = (moduleId: string) => {
		setSelectedModuleId(moduleId)
	}

	// 添加新模块
	const handleAddModule = (moduleIds: string[]) => {
		// 获取已存在的模块ID（排除已删除的模块）
		const existingIds = new Set(
			contentData.modules
				.filter(m => m.operation !== "delete")
				.map(m => m.tabId)
		);
		const newModules: ContentModule[] = [];
		let lastAddedModuleId = "";

		// 创建一个映射，保存每个模块ID在原始选择列表中的顺序
		const moduleIdOrderMap = new Map<string, number>();
		moduleIds.forEach((id, index) => {
			moduleIdOrderMap.set(id, index);
		});

		// 过滤出未存在的模块ID
		const newModuleIds = moduleIds.filter(id => !existingIds.has(id));

		// 按照原始选择顺序创建新模块
		newModuleIds.forEach(moduleId => {
			const moduleInfo = availableModules(currentLanguage).find(
				(m) => m.tabId === moduleId,
			);

			// 根据模块类型设置默认内容
			let defaultText = `在这里编写 MDX 内容...`;
			let defaultJsonContent = {};
			let moduleType = GameDetailContentType.Article;

			if (moduleId === "faq") {
				defaultText = "";
				defaultJsonContent = {
					faqs: [
						{
							question: "这个游戏怎么玩？",
							answer: "这是一个示例问题，请编辑或删除此内容。"
						}
					]
				};
			} else if (moduleId === "userComments") {
				defaultText = "";
				moduleType = GameDetailContentType.Comments;
				defaultJsonContent = {
					comments: [
						{
							name: "示例用户",
							rating: 5,
							content: "这是一个很棒的游戏！非常推荐大家试试。",
							publishDate: new Date().toISOString().split('T')[0]
						}
					]
				};
			}

			const newModule: ContentModule = {
				tabId: moduleId,
				type: moduleType,
				title: moduleInfo?.title || "",
				icon: moduleInfo?.icon || "",
				text: defaultText,
				jsonContent: defaultJsonContent,
				operation: "create",
			};
			newModules.push(newModule);
			lastAddedModuleId = newModule.tabId;
		});

		if (newModules.length > 0) {
			updateContentData({
				modules: [...contentData.modules, ...newModules],
			});

			// 选择最后添加的模块
			setSelectedModuleId(lastAddedModuleId);
			toast.success(`成功添加 ${newModules.length} 个内容模块`);
		} else {
			toast.info("所选模块类型已存在，未添加新模块");
		}
	};

	// 状态管理多选模块对话框
	const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false);
	const [selectedModuleTypes, setSelectedModuleTypes] = useState<string[]>([]);

	// 当对话框打开时，默认选中所有未存在的模块
	const handleOpenModuleDialog = () => {
		// 获取已存在的模块ID（排除已删除的模块）
		const existingIds = new Set(
			contentData.modules
				.filter(m => m.operation !== "delete")
				.map(m => m.tabId)
		);

		// 默认选中所有未存在的模块
		const defaultSelectedIds = availableModules(currentLanguage)
			.filter(module => !existingIds.has(module.tabId))
			.map(module => module.tabId);

		setSelectedModuleTypes(defaultSelectedIds);
		setIsModuleDialogOpen(true);
	};

	// 处理模块类型选择
	const handleModuleTypeToggle = (moduleType: string) => {
		setSelectedModuleTypes(prev =>
			prev.includes(moduleType)
				? prev.filter(type => type !== moduleType)
				: [...prev, moduleType]
		);
	};

	// 确认添加选中的模块
	const confirmAddModules = () => {
		if (selectedModuleTypes.length > 0) {
			handleAddModule(selectedModuleTypes);
		}
		setIsModuleDialogOpen(false);
		setSelectedModuleTypes([]);
	};

	// 更新模块
	const handleUpdateModule = (
		moduleId: string,
		data: Partial<ContentModule>,
	) => {
		const updatedModules = contentData.modules.map((module) => {
			if (module.tabId === moduleId) {
				// 否则设置为update（除非已经是delete状态）
				const operation =
					module.operation === "delete"
						? ("delete" as const)
						: module.operation === "create"
							? ("create" as const)
							: ("update" as const)

				return { ...module, ...data, operation }
			}
			return module
		})

		updateContentData({ modules: updatedModules })
	}

	// 删除模块
	const handleDeleteModule = (moduleId: string) => {
		// 标记模块为删除状态，而不是从数组中移除
		const updatedModules = contentData.modules.map((module) =>
			module.tabId === moduleId
				? { ...module, operation: "delete" as const }
				: module,
		)

		// 获取未删除的模块列表（用于重新选择模块）
		const nonDeletedModules = updatedModules.filter(
			(module) => module.operation !== "delete",
		)

		updateContentData({ modules: updatedModules })

		// 如果删除的是当前选中的模块，则选择另一个模块
		if (selectedModuleId === moduleId) {
			setSelectedModuleId(
				nonDeletedModules.length > 0 ? nonDeletedModules[0]!.tabId : null,
			)
		}
	}

	// 移动模块
	const handleMoveModule = (moduleId: string, direction: "up" | "down" | number) => {
		// 过滤掉已删除的模块，只对可见模块进行排序
		const visibleModules = contentData.modules.filter(
			(m) => m.operation !== "delete",
		)
		const moduleIndex = visibleModules.findIndex((m) => m.tabId === moduleId)
		if (moduleIndex === -1) return

		let newIndex: number

		// 如果传入的是数字，则直接使用该数字作为新索引
		if (typeof direction === "number") {
			// 确保新索引在有效范围内
			newIndex = Math.max(0, Math.min(direction, visibleModules.length - 1))
		} else {
			// 兼容原有的上下移动逻辑
			if (direction === "up" && moduleIndex > 0) {
				// 向上移动
				newIndex = moduleIndex - 1
			} else if (
				direction === "down" &&
				moduleIndex < visibleModules.length - 1
			) {
				// 向下移动
				newIndex = moduleIndex + 1
			} else {
				return // 无法移动
			}
		}

		// 如果新旧索引相同，则不需要移动
		if (newIndex === moduleIndex) return

		// 使用 arrayMove 函数重新排序可见模块
		const newVisibleModules = arrayMove(visibleModules, moduleIndex, newIndex)

		// 更新可见模块的顺序
		const reorderedVisibleModules = newVisibleModules.map(
			(module: ContentModule, index: number) => ({
				...module,
				order: index + 1,
			}),
		)

		// 合并已删除的模块和重新排序的可见模块
		const deletedModules = contentData.modules.filter(
			(m) => m.operation === "delete",
		)
		const updatedModules = [...reorderedVisibleModules, ...deletedModules]

		updateContentData({ modules: updatedModules })
	}

	// 切换模块启用状态
	const handleToggleModuleEnabled = (moduleId: string) => {
		const module = contentData.modules.find((m) => m.tabId === moduleId)
		if (module) {
			handleUpdateModule(moduleId, {})
		}
	}

	// 添加表单验证错误的检查和输出
	useEffect(() => {
		console.log("表单验证错误:", form.formState.errors)
		console.log("表单值:", form.getValues())
	}, [form.formState])

	// 表单提交处理
	const onSubmit = async (formData: z.infer<typeof formSchema>) => {
		console.log("表单提交触发", formData)
		try {
			await handleSave(formData)
			toast.success(gameId === "new-game" ? "游戏内容创建成功" : "游戏内容更新成功")
		} catch (error) {
			console.error("保存游戏内容失败:", error)
			toast.error("保存游戏内容失败")
		}
	}

	// 准备翻译功能
	const handleTranslate = useCallback(
		async (
			selectedLanguages: ProjectLanguage[],
			params?: Record<string, any>,
		) => {
			if (!selectedModuleId) {
				return {
					success: false,
					error: "请先选择一个模块",
				}
			}
			if (selectOriginalModule?.operation === "create") {
				return {
					success: false,
					error: "请先保存后再执行翻译",
				}
			}

			try {
				const response = await fetchPost(
					"/api/project-games/content/translate",
					{
						sourceLocale: defaultLanguage,
						targetLocales: selectedLanguages,
						type: "content",
						id: selectedModuleId,
					},
				)
				trigger({ id: selectedModuleId, locale: selectedLanguages[0] } as any)
				return {
					success: true,
					results: {
						success: response?.success || [],
						failed: response?.failed || [],
					},
				}
			} catch (error) {
				console.error("翻译游戏内容失败:", error)
				return {
					success: false,
					error: "翻译失败",
				}
			}
		},
		[projectId, gameId, defaultLanguage, selectedModuleId],
	)

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pb-20">
				<div className="flex flex-col md:flex-row gap-6">
					{/* 左侧模块列表 */}
					<div className="w-full md:w-1/3 ">
						<Card className="min-h-[480px]">
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle>{t("contentModules")}</CardTitle>
								<Button type="button" variant="outline" size="sm" onClick={handleOpenModuleDialog}>
								<Plus className="w-4 h-4 mr-2" />
								{t("addModule")}
							</Button>

							<Dialog open={isModuleDialogOpen} onOpenChange={setIsModuleDialogOpen}>
								<DialogContent>
									<DialogHeader>
										<DialogTitle>{t("selectModules")}</DialogTitle>
									</DialogHeader>
									<div className="grid gap-4 py-4">
										{availableModules(currentLanguage).map((module) => {
											// 检查该类型模块是否已存在（排除已删除的模块）
											const isExisting = contentData.modules.some(
												m => m.tabId === module.tabId && m.operation !== "delete"
											);
											return (
												<div
													key={module.tabId}
													className={cn(
														"flex items-center p-3 rounded-md border",
														isExisting ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
														selectedModuleTypes.includes(module.tabId)
															? "border-primary bg-primary/5"
															: "border-border"
													)}
													onClick={() => {
														if (!isExisting) {
															handleModuleTypeToggle(module.tabId);
														}
													}}
												>
													<div className="flex items-center space-x-3 flex-1">
														<div className="flex items-center justify-center w-6 h-6">
															<Icon
																name={module.icon}
																size={20}
															/>
														</div>
														<div>
															<div className="font-medium">{module.name}</div>
															<div className="text-xs text-muted-foreground">
																{isExisting ? "已添加" : module.description}
															</div>
														</div>
													</div>
													{!isExisting && (
														<Checkbox
															checked={selectedModuleTypes.includes(module.tabId)}
															onCheckedChange={() => handleModuleTypeToggle(module.tabId)}
														/>
													)}
												</div>
											);
										})}
									</div>
									<DialogFooter>
										<Button type="button" variant="outline" onClick={() => setIsModuleDialogOpen(false)}>
											{t("cancel")}
										</Button>
										<Button type="button" onClick={confirmAddModules} disabled={selectedModuleTypes.length === 0}>
											{t("confirm")}
										</Button>
									</DialogFooter>
								</DialogContent>
							</Dialog>
							</CardHeader>
							<CardContent>
								{contentData.modules.length > 0 ? (
									<ContentModuleList
										modules={contentData.modules}
										selectedModuleId={selectedModuleId}
										onSelectModule={handleSelectModule}
										onDeleteModule={handleDeleteModule}
										onMoveModule={handleMoveModule}
										onToggleEnabled={handleToggleModuleEnabled}
									/>
								) : (
									<div className="text-center py-8 text-muted-foreground">
										{t("noContentModules")}
									</div>
								)}
							</CardContent>
						</Card>
					</div>

					{/* 右侧编辑区域 */}
					<div className="w-full md:w-2/3">
						<Card className="h-full ">
							{selectedModule ? (
								<>
									<CardHeader>
										<div className="flex justify-between items-start mb-4">
											<div>
												<CardTitle>{selectOriginalModule?.title}</CardTitle>
												<CardDescription>
													{availableModules(currentLanguage).find(
														(m) => m.tabId === selectOriginalModule?.tabId,
													)?.description}
												</CardDescription>
											</div>
											<TranslationSwitcher
												onLanguageChange={(code) => {
													trigger({
														id: selectedModuleId,
														locale: code,
													} as any)
												}}
												onTranslate={handleTranslate}
											/>
										</div>
									</CardHeader>
									<CardContent>
										<div className="space-y-6">
											<div className="grid md:grid-cols-2 gap-4">
												<div className="space-y-2">
													<Label htmlFor="module-title">
														{t("moduleTitle")}
													</Label>
													<Input
														id="module-title"
														value={selectedModule.title}
														onChange={(e) =>
															handleUpdateModule(selectedModule.tabId, {
																title: e.target.value,
															})
														}
														placeholder={t("enterModuleTitle")}
													/>
												</div>
												{locale === ProjectLanguage.EN ? (
													<div className="space-y-2">
														<Label htmlFor="module-icon">{t("moduleIcon")}</Label>
														<IconSelector
															value={selectedModule.icon || ""}
															onChange={(value) => {
																handleUpdateModule(selectedModule.tabId, {
																	icon: value,
																})
															}}
															projectId={projectId}
															allowUpload={true}
														/>
													</div>
												) : (
													<div className="space-y-2">
														<Label htmlFor="module-icon">{t("moduleIcon")}</Label>
														<div className="flex items-center h-10 px-3 border rounded-md bg-muted/50 text-muted-foreground">
															{selectOriginalModule?.icon ? (
																<div className="flex items-center gap-2">
																	<Icon name={selectOriginalModule.icon} size={16} />
																	<span>{selectOriginalModule.icon}</span>
																</div>
															) : (
																<span>{t("noIconSelected")}</span>
															)}
														</div>
														<p className="text-xs text-muted-foreground">{t("iconOnlyEditableInDefaultLanguage")}</p>
													</div>
												)}
											</div>
											{/* 根据模块类型渲染不同的编辑器 */}
											{selectedModule.tabId === "faq" ? (
												<FAQEditor
													faqs={(selectedModule.jsonContent as any)?.faqs || []}
													onChange={(faqs) =>
														handleUpdateModule(selectedModule.tabId, {
															jsonContent: { faqs },
														})
													}
												/>
											) : selectedModule.tabId === "userComments" ? (
												<UserCommentsEditor
													comments={(selectedModule.jsonContent as any)?.comments || []}
													onChange={(comments) =>
														handleUpdateModule(selectedModule.tabId, {
															jsonContent: { comments },
														})
													}
												/>
											) : (
												<div className="space-y-2">
													<div className="flex items-center justify-between">
														<Label htmlFor="mdx-content">{t("mdxContent")}</Label>
														<span className="text-xs text-muted-foreground">{t("mdxContentDescription")}</span>
													</div>
													<MdxEditor
														initialContent={selectedModule.text || ""}
														value={selectedModule.text || ""}
														onChange={(value) =>
															handleUpdateModule(selectedModule.tabId, {
																text: value,
															})
														}
														minHeight="400px"
														variant="default"
														showToolbar={true}
													/>
													<p className="text-xs text-muted-foreground">
														{t("mdxContentHelp")}
													</p>
												</div>
											)}
										</div>
									</CardContent>
								</>
							) : (
								<div className="flex items-center justify-center h-full">
									<div className="text-center p-8 text-muted-foreground">
										{contentData.modules.length > 0
											? t("selectModuleToEdit")
											: t("addModuleToStart")}
									</div>
								</div>
							)}
						</Card>
					</div>
				</div>

				{/* 底部固定的保存按钮 */}
				<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
					<div className="max-w-6xl mx-auto flex justify-end w-full">
						<Button
							type="reset"
							variant="outline"
							disabled={isLoading}
							className="mr-3"
						>
							{t("reset")}
						</Button>
						<Button
							type="submit"
							loading={isLoading}
							className="cursor-pointer"
							onClick={() => {
								console.log('点击了提交按钮')
								// 手动触发表单提交
								form.handleSubmit(onSubmit)()
							}}
						>
							{isLoading ? t("saving") : t("saveSettings")}
						</Button>
					</div>
				</div>
			</form>
		</Form>
	)
}
